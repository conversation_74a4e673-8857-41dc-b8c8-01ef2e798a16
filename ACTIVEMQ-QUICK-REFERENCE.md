# 🚀 ActiveMQ Quick Reference Card

## 🔗 **Instant Access**
- **Console**: http://localhost:8161/admin/ (admin/admin)
- **Queues**: http://localhost:8161/admin/queues.jsp
- **Send Message**: http://localhost:8161/admin/send.jsp

## 📊 **Daily Health Check (2 minutes)**

### **Step 1: Check Queue Status**
```
Go to: http://localhost:8161/admin/queues.jsp
Look for: "Number Of Pending Messages" column
✅ Normal: 0-10 messages
🚨 Alert: >50 messages
```

### **Step 2: Check Consumers**
```
Look for: "Number Of Consumers" column
✅ Normal: 1+ consumers per queue
🚨 Alert: 0 consumers (no processing)
```

### **Step 3: Check Dead Letter Queues**
```
Look for: DLQ.* queues
✅ Normal: 0 messages
🚨 Alert: Any messages (failed operations)
```

## 🔍 **Todo Application Queues**

| Queue Name | Purpose | Normal State |
|------------|---------|--------------|
| `todo.create.queue` | New todos | 0-5 pending |
| `todo.update.queue` | Todo updates | 0-5 pending |
| `todo.delete.queue` | Todo deletions | 0-2 pending |
| `todo.toggle.queue` | Status changes | 0-3 pending |
| `todo.audit.queue` | Audit logs | 0-10 pending |
| `DLQ.todo.*` | Failed operations | **0 messages** |

## 🚨 **Emergency Actions**

### **High Pending Messages**
```bash
1. Check database: docker exec oracle sqlplus system/SavvySpend123@XEPDB1
2. Reset circuit breaker: http://localhost:9080/todo-list-app/resilience/dashboard
3. Restart app: docker restart appserver
```

### **Messages in DLQ**
```bash
1. Browse DLQ: Click queue name → Browse
2. Copy message content
3. Send to original queue: http://localhost:8161/admin/send.jsp
4. Delete from DLQ
```

### **No Consumers**
```bash
1. Restart WebSphere: docker restart appserver
2. Check JMS config in WebSphere console
3. Verify ActiveMQ connectivity
```

## 📨 **Message Operations**

### **View Pending Messages**
```
1. Go to Queues tab
2. Click queue name (e.g., todo.create.queue)
3. Click "Browse" to see message content
```

### **Send Test Message**
```json
// Go to: http://localhost:8161/admin/send.jsp
// Destination: todo.create.queue
{
  "operation": "CREATE_TODO",
  "userId": "1",
  "title": "Test Todo",
  "description": "Testing queue",
  "priority": "MEDIUM"
}
```

### **Retry Failed Messages**
```
1. Browse DLQ queue
2. Copy message JSON
3. Send to original queue
4. Delete from DLQ
```

## 🔧 **Quick Commands**

### **Health Check**
```bash
# ActiveMQ status
curl -u admin:admin http://localhost:8161/admin/

# Queue statistics
curl -u admin:admin http://localhost:8161/admin/xml/queues.jsp
```

### **Container Management**
```bash
# Check containers
docker ps | grep -E "(activemq|appserver|oracle)"

# Restart ActiveMQ
docker restart activemq

# View logs
docker logs activemq --tail 20
```

## 📈 **Monitoring Thresholds**

| Metric | Green | Yellow | Red |
|--------|-------|--------|-----|
| Pending Messages | 0-10 | 11-50 | >50 |
| DLQ Messages | 0 | 1-5 | >5 |
| Consumers | 1+ | 0 (temp) | 0 (persistent) |
| Memory Usage | <70% | 70-85% | >85% |

## 🎯 **Daily Checklist**

### **Morning (5 minutes)**
- [ ] Open ActiveMQ console
- [ ] Check all queue depths
- [ ] Verify consumer connections
- [ ] Check DLQ for failures
- [ ] Review overnight processing

### **Evening (3 minutes)**
- [ ] Check daily message volumes
- [ ] Clear any DLQ messages
- [ ] Verify system stability
- [ ] Note any anomalies

## 🆘 **Escalation**

### **When to Escalate**
- Queue depth >100 messages for >30 minutes
- DLQ messages increasing continuously
- No consumers for >15 minutes
- ActiveMQ memory >90%
- Database connection failures

### **Information to Collect**
- Queue statistics screenshot
- Recent error messages
- Application logs
- Database connectivity status
- System resource usage

---

**💡 Pro Tip: Bookmark http://localhost:8161/admin/queues.jsp for instant queue monitoring!**
