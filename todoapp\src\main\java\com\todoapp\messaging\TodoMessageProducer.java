package com.todoapp.messaging;

import com.todoapp.entity.Todo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import javax.annotation.Resource;
import javax.jms.*;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NameNotFoundException;
import javax.naming.NamingException;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.HashMap;
import java.util.Map;

/**
 * Message Producer for Todo Operations
 * Sends todo operations to ActiveMQ queues for asynchronous processing
 */
public class TodoMessageProducer {
    
    private static final Logger logger = Logger.getLogger(TodoMessageProducer.class.getName());
    
    // Queue names
    private static final String CREATE_QUEUE = "todo.create.queue";
    private static final String UPDATE_QUEUE = "todo.update.queue";
    private static final String DELETE_QUEUE = "todo.delete.queue";
    private static final String TOGGLE_QUEUE = "todo.toggle.queue";
    private static final String AUDIT_QUEUE = "todo.audit.queue";
    
    // Topic names
    private static final String NOTIFICATION_TOPIC = "todo.notifications";
    
    @Resource(mappedName = "jms/TodoConnectionFactory")
    private ConnectionFactory connectionFactory;
    
    private ObjectMapper objectMapper;
    
    public TodoMessageProducer() {
        // Initialize JSON mapper for message serialization
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }
    
    /**
     * Initialize JMS resources using JNDI lookup
     */
    private void initializeJMS() {
        if (connectionFactory == null) {
            try {
                Context ctx = new InitialContext();
                connectionFactory = (ConnectionFactory) ctx.lookup("jms/TodoConnectionFactory");
                logger.info("✅ JMS ConnectionFactory initialized via JNDI");
            } catch (NamingException e) {
                logger.log(Level.SEVERE, "❌ Failed to lookup JMS ConnectionFactory", e);
                throw new RuntimeException("Failed to initialize JMS", e);
            }
        }
    }
    
    /**
     * Send todo creation message to queue
     */
    public void sendCreateTodoMessage(Todo todo, String userId) {
        try {
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("operation", "CREATE");
            messageData.put("todo", todo);
            messageData.put("userId", userId);
            messageData.put("timestamp", System.currentTimeMillis());
            
            sendMessage(CREATE_QUEUE, messageData, "TodoCreate");
            
            // Send notification
            sendNotificationMessage("Todo created: " + todo.getTitle(), userId);
            
            // Send audit message
            sendAuditMessage("CREATE", todo.getTodoId(), userId);
            
            logger.info("✅ Create todo message sent for: " + todo.getTitle());
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "❌ Failed to send create todo message", e);
            throw new RuntimeException("Failed to send create message", e);
        }
    }
    
    /**
     * Send todo update message to queue
     */
    public void sendUpdateTodoMessage(Todo todo, String userId) {
        try {
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("operation", "UPDATE");
            messageData.put("todo", todo);
            messageData.put("userId", userId);
            messageData.put("timestamp", System.currentTimeMillis());
            
            sendMessage(UPDATE_QUEUE, messageData, "TodoUpdate");
            
            // Send notification
            sendNotificationMessage("Todo updated: " + todo.getTitle(), userId);
            
            // Send audit message
            sendAuditMessage("UPDATE", todo.getTodoId(), userId);
            
            logger.info("✅ Update todo message sent for: " + todo.getTitle());
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "❌ Failed to send update todo message", e);
            throw new RuntimeException("Failed to send update message", e);
        }
    }
    
    /**
     * Send todo toggle status message to queue
     */
    public void sendToggleTodoMessage(Long todoId, String userId) {
        try {
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("operation", "TOGGLE");
            messageData.put("todoId", todoId);
            messageData.put("userId", userId);
            messageData.put("timestamp", System.currentTimeMillis());
            
            sendMessage(TOGGLE_QUEUE, messageData, "TodoToggle");
            
            // Send notification
            sendNotificationMessage("Todo status toggled for ID: " + todoId, userId);
            
            // Send audit message
            sendAuditMessage("TOGGLE", todoId, userId);
            
            logger.info("✅ Toggle todo message sent for ID: " + todoId);
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "❌ Failed to send toggle todo message", e);
            throw new RuntimeException("Failed to send toggle message", e);
        }
    }
    
    /**
     * Send todo deletion message to queue
     */
    public void sendDeleteTodoMessage(Long todoId, String userId) {
        try {
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("operation", "DELETE");
            messageData.put("todoId", todoId);
            messageData.put("userId", userId);
            messageData.put("timestamp", System.currentTimeMillis());
            
            sendMessage(DELETE_QUEUE, messageData, "TodoDelete");
            
            // Send notification
            sendNotificationMessage("Todo deleted: ID " + todoId, userId);
            
            // Send audit message
            sendAuditMessage("DELETE", todoId, userId);
            
            logger.info("✅ Delete todo message sent for ID: " + todoId);
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "❌ Failed to send delete todo message", e);
            throw new RuntimeException("Failed to send delete message", e);
        }
    }
    
    /**
     * Send notification message to topic
     */
    private void sendNotificationMessage(String message, String userId) {
        try {
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("message", message);
            notificationData.put("userId", userId);
            notificationData.put("timestamp", System.currentTimeMillis());
            notificationData.put("type", "TODO_OPERATION");
            
            sendMessageToTopic(NOTIFICATION_TOPIC, notificationData, "TodoNotification");
            
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to send notification message", e);
            // Don't throw exception for notifications - they're not critical
        }
    }
    
    /**
     * Send audit message to queue
     */
    private void sendAuditMessage(String operation, Long todoId, String userId) {
        try {
            Map<String, Object> auditData = new HashMap<>();
            auditData.put("operation", operation);
            auditData.put("todoId", todoId);
            auditData.put("userId", userId);
            auditData.put("timestamp", System.currentTimeMillis());
            auditData.put("source", "TodoApp");
            
            sendMessage(AUDIT_QUEUE, auditData, "TodoAudit");
            
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to send audit message", e);
            // Don't throw exception for audit - they're not critical
        }
    }
    
    /**
     * Generic method to send message to a queue
     */
    private void sendMessage(String queueName, Map<String, Object> messageData, String messageType) 
            throws JMSException, NamingException {
        
        initializeJMS();
        
        Connection connection = null;
        Session session = null;
        MessageProducer producer = null;
        
        try {
            // Create connection and session
            connection = connectionFactory.createConnection();
            session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);
            
            // Lookup or create queue
            Context ctx = new InitialContext();
            Queue queue = null;
            try {
                queue = (Queue) ctx.lookup("jms/" + queueName);
            } catch (NameNotFoundException e) {
                logger.info("Queue not found in JNDI, attempting to create: " + queueName);
                // Try to create queue dynamically
                queue = session.createQueue(queueName);
            }
            
            // Create producer and message
            producer = session.createProducer(queue);
            TextMessage message = session.createTextMessage();
            
            // Set message body as JSON
            String jsonMessage = objectMapper.writeValueAsString(messageData);
            message.setText(jsonMessage);
            
            // Set message properties
            message.setStringProperty("MessageType", messageType);
            message.setStringProperty("Source", "TodoApp");
            message.setLongProperty("Timestamp", System.currentTimeMillis());
            
            // Send message
            producer.send(message);
            
            logger.fine("Message sent to queue: " + queueName);
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to send message to queue: " + queueName, e);
            throw new JMSException("Failed to send message: " + e.getMessage());
        } finally {
            // Clean up resources
            if (producer != null) producer.close();
            if (session != null) session.close();
            if (connection != null) connection.close();
        }
    }
    
    /**
     * Generic method to send message to a topic
     */
    private void sendMessageToTopic(String topicName, Map<String, Object> messageData, String messageType) 
            throws JMSException, NamingException {
        
        initializeJMS();
        
        Connection connection = null;
        Session session = null;
        MessageProducer producer = null;
        
        try {
            // Create connection and session
            connection = connectionFactory.createConnection();
            session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);
            
            // Lookup topic
            Context ctx = new InitialContext();
            Topic topic = (Topic) ctx.lookup("jms/" + topicName);
            
            // Create producer and message
            producer = session.createProducer(topic);
            TextMessage message = session.createTextMessage();
            
            // Set message body as JSON
            String jsonMessage = objectMapper.writeValueAsString(messageData);
            message.setText(jsonMessage);
            
            // Set message properties
            message.setStringProperty("MessageType", messageType);
            message.setStringProperty("Source", "TodoApp");
            message.setLongProperty("Timestamp", System.currentTimeMillis());
            
            // Send message
            producer.send(message);
            
            logger.fine("Message sent to topic: " + topicName);
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to send message to topic: " + topicName, e);
            throw new JMSException("Failed to send message: " + e.getMessage());
        } finally {
            // Clean up resources
            if (producer != null) producer.close();
            if (session != null) session.close();
            if (connection != null) connection.close();
        }
    }
}
