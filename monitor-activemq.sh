#!/bin/bash

# ActiveMQ Monitoring Script for Todo Application
# This script provides automated monitoring and alerting for ActiveMQ queues

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ACTIVEMQ_URL="http://localhost:8161"
ACTIVEMQ_USER="admin"
ACTIVEMQ_PASS="admin"
ALERT_THRESHOLD_PENDING=50
ALERT_THRESHOLD_DLQ=1

# Function to print colored output
print_status() {
    local status="$1"
    local message="$2"
    
    case $status in
        "OK")
            echo -e "${GREEN}✅ OK${NC}: $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️ WARNING${NC}: $message"
            ;;
        "ERROR")
            echo -e "${RED}❌ ERROR${NC}: $message"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️ INFO${NC}: $message"
            ;;
    esac
}

# Function to check ActiveMQ connectivity
check_activemq_connectivity() {
    echo "🔍 Checking ActiveMQ Connectivity..."
    
    if curl -s -u "$ACTIVEMQ_USER:$ACTIVEMQ_PASS" "$ACTIVEMQ_URL/admin/" > /dev/null 2>&1; then
        print_status "OK" "ActiveMQ is accessible at $ACTIVEMQ_URL"
        return 0
    else
        print_status "ERROR" "Cannot connect to ActiveMQ at $ACTIVEMQ_URL"
        return 1
    fi
}

# Function to get queue statistics
get_queue_stats() {
    local queue_name="$1"
    
    # Get queue XML data
    local xml_data=$(curl -s -u "$ACTIVEMQ_USER:$ACTIVEMQ_PASS" "$ACTIVEMQ_URL/admin/xml/queues.jsp" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to fetch queue data"
        return 1
    fi
    
    # Extract queue statistics using grep and sed
    local queue_info=$(echo "$xml_data" | grep -A 10 "name=\"$queue_name\"" | head -15)
    
    if [ -z "$queue_info" ]; then
        echo "0,0,0,0" # pending,consumers,enqueued,dequeued
        return 0
    fi
    
    local pending=$(echo "$queue_info" | grep -o 'size="[0-9]*"' | cut -d'"' -f2 | head -1)
    local consumers=$(echo "$queue_info" | grep -o 'consumerCount="[0-9]*"' | cut -d'"' -f2 | head -1)
    local enqueued=$(echo "$queue_info" | grep -o 'enqueueCount="[0-9]*"' | cut -d'"' -f2 | head -1)
    local dequeued=$(echo "$queue_info" | grep -o 'dequeueCount="[0-9]*"' | cut -d'"' -f2 | head -1)
    
    # Default to 0 if values are empty
    pending=${pending:-0}
    consumers=${consumers:-0}
    enqueued=${enqueued:-0}
    dequeued=${dequeued:-0}
    
    echo "$pending,$consumers,$enqueued,$dequeued"
}

# Function to monitor todo queues
monitor_todo_queues() {
    echo ""
    echo "📊 Todo Application Queue Status"
    echo "================================="
    
    local queues=(
        "todo.create.queue"
        "todo.update.queue"
        "todo.delete.queue"
        "todo.toggle.queue"
        "todo.audit.queue"
    )
    
    local total_pending=0
    local total_issues=0
    
    printf "%-20s %-8s %-9s %-9s %-9s %-6s\n" "Queue" "Pending" "Consumers" "Enqueued" "Dequeued" "Status"
    printf "%-20s %-8s %-9s %-9s %-9s %-6s\n" "--------------------" "--------" "---------" "---------" "---------" "------"
    
    for queue in "${queues[@]}"; do
        local stats=$(get_queue_stats "$queue")
        IFS=',' read -r pending consumers enqueued dequeued <<< "$stats"
        
        total_pending=$((total_pending + pending))
        
        # Determine status
        local status="OK"
        local status_color="${GREEN}"
        
        if [ "$pending" -gt "$ALERT_THRESHOLD_PENDING" ]; then
            status="HIGH"
            status_color="${RED}"
            total_issues=$((total_issues + 1))
        elif [ "$pending" -gt 10 ]; then
            status="WARN"
            status_color="${YELLOW}"
        fi
        
        if [ "$consumers" -eq 0 ]; then
            status="NO_CONS"
            status_color="${RED}"
            total_issues=$((total_issues + 1))
        fi
        
        printf "%-20s %-8s %-9s %-9s %-9s " "$queue" "$pending" "$consumers" "$enqueued" "$dequeued"
        echo -e "${status_color}$status${NC}"
    done
    
    echo ""
    echo "📈 Summary: $total_pending total pending messages, $total_issues issues detected"
}

# Function to monitor dead letter queues
monitor_dlq() {
    echo ""
    echo "💀 Dead Letter Queue Status"
    echo "============================"
    
    local dlq_queues=(
        "DLQ.todo.create.queue"
        "DLQ.todo.update.queue"
        "DLQ.todo.delete.queue"
        "DLQ.todo.toggle.queue"
    )
    
    local total_dlq=0
    local dlq_issues=0
    
    printf "%-25s %-8s %-6s\n" "DLQ Queue" "Messages" "Status"
    printf "%-25s %-8s %-6s\n" "-------------------------" "--------" "------"
    
    for queue in "${dlq_queues[@]}"; do
        local stats=$(get_queue_stats "$queue")
        IFS=',' read -r pending consumers enqueued dequeued <<< "$stats"
        
        total_dlq=$((total_dlq + pending))
        
        local status="OK"
        local status_color="${GREEN}"
        
        if [ "$pending" -gt 0 ]; then
            status="FAILED"
            status_color="${RED}"
            dlq_issues=$((dlq_issues + 1))
        fi
        
        printf "%-25s %-8s " "$queue" "$pending"
        echo -e "${status_color}$status${NC}"
    done
    
    echo ""
    if [ "$total_dlq" -gt 0 ]; then
        print_status "WARNING" "$total_dlq failed messages in dead letter queues - investigate immediately"
    else
        print_status "OK" "No failed messages in dead letter queues"
    fi
}

# Function to check system health
check_system_health() {
    echo ""
    echo "🏥 System Health Check"
    echo "======================"
    
    # Check ActiveMQ memory usage via JMX
    local memory_info=$(curl -s -u "$ACTIVEMQ_USER:$ACTIVEMQ_PASS" \
        "$ACTIVEMQ_URL/api/jolokia/read/java.lang:type=Memory" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$memory_info" ]; then
        print_status "OK" "ActiveMQ JMX endpoint accessible"
    else
        print_status "WARNING" "Cannot access ActiveMQ JMX metrics"
    fi
    
    # Check broker status
    local broker_info=$(curl -s -u "$ACTIVEMQ_USER:$ACTIVEMQ_PASS" \
        "$ACTIVEMQ_URL/api/jolokia/read/org.apache.activemq:type=Broker,brokerName=localhost" 2>/dev/null)
    
    if echo "$broker_info" | grep -q '"BrokerName":"localhost"'; then
        print_status "OK" "ActiveMQ broker is running normally"
    else
        print_status "WARNING" "ActiveMQ broker status unclear"
    fi
    
    # Check database connectivity (if Oracle container is running)
    if docker ps | grep -q "oracle.*Up"; then
        print_status "OK" "Oracle database container is running"
    else
        print_status "WARNING" "Oracle database container not running"
    fi
    
    # Check WebSphere connectivity
    if docker ps | grep -q "appserver.*Up"; then
        print_status "OK" "WebSphere application server is running"
    else
        print_status "WARNING" "WebSphere application server not running"
    fi
}

# Function to provide recommendations
provide_recommendations() {
    echo ""
    echo "💡 Recommendations"
    echo "=================="
    
    # Check for high pending messages
    local total_pending=$(curl -s -u "$ACTIVEMQ_USER:$ACTIVEMQ_PASS" "$ACTIVEMQ_URL/admin/xml/queues.jsp" 2>/dev/null | \
        grep -o 'size="[0-9]*"' | cut -d'"' -f2 | awk '{sum += $1} END {print sum+0}')
    
    if [ "$total_pending" -gt 50 ]; then
        echo "🚨 High message volume detected ($total_pending pending)"
        echo "   → Check database connectivity"
        echo "   → Verify circuit breaker status: http://localhost:9080/todo-list-app/resilience/dashboard"
        echo "   → Consider restarting consumers: docker restart appserver"
    fi
    
    # Check for DLQ messages
    local dlq_count=$(curl -s -u "$ACTIVEMQ_USER:$ACTIVEMQ_PASS" "$ACTIVEMQ_URL/admin/xml/queues.jsp" 2>/dev/null | \
        grep -A 5 'name="DLQ\.' | grep -o 'size="[0-9]*"' | cut -d'"' -f2 | awk '{sum += $1} END {print sum+0}')
    
    if [ "$dlq_count" -gt 0 ]; then
        echo "💀 Failed messages detected ($dlq_count in DLQ)"
        echo "   → Investigate message content: http://localhost:8161/admin/queues.jsp"
        echo "   → Check application logs for errors"
        echo "   → Retry messages after fixing root cause"
    fi
    
    # General recommendations
    echo ""
    echo "📋 Daily Actions:"
    echo "   • Monitor queue depths: http://localhost:8161/admin/queues.jsp"
    echo "   • Check resilience dashboard: http://localhost:9080/todo-list-app/resilience/dashboard"
    echo "   • Review application logs for errors"
    echo "   • Verify database connectivity"
}

# Main execution
main() {
    echo "🚀 ActiveMQ Todo Application Monitor"
    echo "===================================="
    echo "Timestamp: $(date)"
    echo ""
    
    # Check connectivity first
    if ! check_activemq_connectivity; then
        echo ""
        echo "❌ Cannot proceed without ActiveMQ connectivity"
        echo "💡 Try: docker restart activemq"
        exit 1
    fi
    
    # Run monitoring checks
    monitor_todo_queues
    monitor_dlq
    check_system_health
    provide_recommendations
    
    echo ""
    echo "✅ Monitoring complete - $(date)"
    echo "🔄 Run this script regularly or set up as a cron job"
}

# Execute main function
main "$@"
