# PowerShell script to create ActiveMQ queues for Todo application
# This script creates the necessary queues using ActiveMQ's REST API

Write-Host "=== Creating ActiveMQ Queues for Todo Application ===" -ForegroundColor Green

# ActiveMQ configuration
$activemqUrl = "http://localhost:8161"
$username = "admin"
$password = "admin123"

# Create credentials for authentication
$credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${username}:${password}"))
$headers = @{
    'Authorization' = "Basic $credentials"
    'Content-Type' = 'application/x-www-form-urlencoded'
}

# Function to create a queue
function Create-Queue {
    param(
        [string]$queueName
    )
    
    Write-Host "Creating queue: $queueName" -ForegroundColor Yellow
    
    $body = "JMSDestination=$queueName&JMSDestinationType=queue&secret=&action=create"
    
    try {
        $response = Invoke-WebRequest -Uri "$activemqUrl/admin/queues.jsp" -Method POST -Headers $headers -Body $body -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Queue $queueName created successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to create queue $queueName - Status: $($response.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Failed to create queue $queueName - Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to create a topic
function Create-Topic {
    param(
        [string]$topicName
    )
    
    Write-Host "Creating topic: $topicName" -ForegroundColor Yellow
    
    $body = "JMSDestination=$topicName&JMSDestinationType=topic&secret=&action=create"
    
    try {
        $response = Invoke-WebRequest -Uri "$activemqUrl/admin/topics.jsp" -Method POST -Headers $headers -Body $body -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Topic $topicName created successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to create topic $topicName - Status: $($response.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Failed to create topic $topicName - Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test ActiveMQ connectivity
Write-Host "Testing ActiveMQ connectivity..." -ForegroundColor Yellow
try {
    # Try without authentication first
    $testResponse = Invoke-WebRequest -Uri "$activemqUrl/" -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ ActiveMQ is accessible" -ForegroundColor Green
} catch {
    Write-Host "❌ Cannot connect to ActiveMQ: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure ActiveMQ is running and accessible at $activemqUrl" -ForegroundColor Red
    Write-Host "You may need to create the queues manually through the web console." -ForegroundColor Yellow
    exit 1
}

# Create Todo operation queues
Write-Host "`nCreating Todo operation queues..." -ForegroundColor Cyan
Create-Queue "todo.create.queue"
Create-Queue "todo.update.queue"
Create-Queue "todo.delete.queue"
Create-Queue "todo.toggle.queue"

# Create audit queue
Write-Host "`nCreating audit queue..." -ForegroundColor Cyan
Create-Queue "todo.audit.queue"

# Create Dead Letter Queues
Write-Host "`nCreating Dead Letter Queues..." -ForegroundColor Cyan
Create-Queue "DLQ.todo.create.queue"
Create-Queue "DLQ.todo.update.queue"
Create-Queue "DLQ.todo.delete.queue"
Create-Queue "DLQ.todo.toggle.queue"
Create-Queue "DLQ.todo.audit.queue"

# Create notification topics
Write-Host "`nCreating notification topics..." -ForegroundColor Cyan
Create-Topic "todo.notifications"
Create-Topic "todo.statistics"

Write-Host "`n=== Queue Creation Complete ===" -ForegroundColor Green
Write-Host "You can verify the queues at: $activemqUrl/admin/queues.jsp" -ForegroundColor Yellow
