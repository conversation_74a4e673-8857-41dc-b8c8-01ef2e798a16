#!/usr/bin/env python
# WebSphere JMS Configuration Script for ActiveMQ Integration
# This script configures JMS resources in WebSphere to connect to ActiveMQ

print("=== Configuring WebSphere JMS Resources for ActiveMQ ===")

try:
    # Get the server configuration
    server = AdminConfig.getid('/Server:server1/')
    if not server:
        print("❌ Error: Could not find server1")
        exit(1)
    
    print("✅ Found server: " + server)
    
    # Create JMS Provider for ActiveMQ
    print("\n🔧 Creating ActiveMQ JMS Provider...")
    
    jmsProviderAttrs = [
        ['name', 'ActiveMQProvider'],
        ['description', 'ActiveMQ JMS Provider for Todo Application'],
        ['classpath', '/opt/IBM/WebSphere/AppServer/lib/activemq-all-5.16.7.jar'],
        ['nativepath', '']
    ]
    
    jmsProvider = AdminConfig.create('GenericJMSProvider', server, jmsProviderAttrs)
    print("✅ Created JMS Provider: " + str(jmsProvider))
    
    # Create JMS Connection Factory
    print("\n🔧 Creating JMS Connection Factory...")
    
    connectionFactoryAttrs = [
        ['name', 'TodoConnectionFactory'],
        ['jndiName', 'jms/TodoConnectionFactory'],
        ['description', 'ActiveMQ Connection Factory for Todo Application'],
        ['type', 'javax.jms.ConnectionFactory'],
        ['factoryClass', 'org.apache.activemq.ActiveMQConnectionFactory']
    ]
    
    connectionFactory = AdminConfig.create('GenericJMSConnectionFactory', jmsProvider, connectionFactoryAttrs)
    print("✅ Created Connection Factory: " + str(connectionFactory))
    
    # Set connection factory properties
    print("\n🔧 Setting Connection Factory Properties...")
    
    # Get the property set for the connection factory
    propertySet = AdminConfig.showAttribute(connectionFactory, 'propertySet')
    if not propertySet:
        propertySet = AdminConfig.create('J2EEResourcePropertySet', connectionFactory, [])
    
    # Add brokerURL property
    brokerURLProperty = AdminConfig.create('J2EEResourceProperty', propertySet, [
        ['name', 'brokerURL'],
        ['value', 'tcp://activemq:61616'],
        ['type', 'java.lang.String'],
        ['description', 'ActiveMQ Broker URL']
    ])
    print("✅ Added brokerURL property: " + str(brokerURLProperty))
    
    # Add username property
    usernameProperty = AdminConfig.create('J2EEResourceProperty', propertySet, [
        ['name', 'userName'],
        ['value', 'admin'],
        ['type', 'java.lang.String'],
        ['description', 'ActiveMQ Username']
    ])
    print("✅ Added username property: " + str(usernameProperty))

    # Add password property
    passwordProperty = AdminConfig.create('J2EEResourceProperty', propertySet, [
        ['name', 'password'],
        ['value', 'admin'],
        ['type', 'java.lang.String'],
        ['description', 'ActiveMQ Password']
    ])
    print("✅ Added password property: " + str(passwordProperty))
    
    # Create JMS Queues
    print("\n🔧 Creating JMS Queues...")
    
    queues = [
        ['todo.create.queue', 'jms/todo.create.queue', 'Queue for todo creation operations'],
        ['todo.update.queue', 'jms/todo.update.queue', 'Queue for todo update operations'],
        ['todo.delete.queue', 'jms/todo.delete.queue', 'Queue for todo deletion operations'],
        ['todo.toggle.queue', 'jms/todo.toggle.queue', 'Queue for todo toggle operations'],
        ['todo.audit.queue', 'jms/todo.audit.queue', 'Queue for audit operations']
    ]
    
    for queueName, jndiName, description in queues:
        queueAttrs = [
            ['name', queueName],
            ['jndiName', jndiName],
            ['description', description],
            ['type', 'javax.jms.Queue'],
            ['factoryClass', 'org.apache.activemq.command.ActiveMQQueue']
        ]
        
        queue = AdminConfig.create('GenericJMSDestination', jmsProvider, queueAttrs)
        
        # Set queue properties
        queuePropertySet = AdminConfig.showAttribute(queue, 'propertySet')
        if not queuePropertySet:
            queuePropertySet = AdminConfig.create('J2EEResourcePropertySet', queue, [])
        
        # Add physicalName property
        physicalNameProperty = AdminConfig.create('J2EEResourceProperty', queuePropertySet, [
            ['name', 'physicalName'],
            ['value', queueName],
            ['type', 'java.lang.String'],
            ['description', 'Physical queue name in ActiveMQ']
        ])
        
        print("✅ Created queue: " + queueName + " -> " + jndiName)
    
    # Create JMS Topics
    print("\n🔧 Creating JMS Topics...")
    
    topics = [
        ['todo.notifications', 'jms/todo.notifications', 'Topic for todo notifications'],
        ['todo.statistics', 'jms/todo.statistics', 'Topic for todo statistics']
    ]
    
    for topicName, jndiName, description in topics:
        topicAttrs = [
            ['name', topicName],
            ['jndiName', jndiName],
            ['description', description],
            ['type', 'javax.jms.Topic'],
            ['factoryClass', 'org.apache.activemq.command.ActiveMQTopic']
        ]
        
        topic = AdminConfig.create('GenericJMSDestination', jmsProvider, topicAttrs)
        
        # Set topic properties
        topicPropertySet = AdminConfig.showAttribute(topic, 'propertySet')
        if not topicPropertySet:
            topicPropertySet = AdminConfig.create('J2EEResourcePropertySet', topic, [])
        
        # Add physicalName property
        physicalNameProperty = AdminConfig.create('J2EEResourceProperty', topicPropertySet, [
            ['name', 'physicalName'],
            ['value', topicName],
            ['type', 'java.lang.String'],
            ['description', 'Physical topic name in ActiveMQ']
        ])
        
        print("✅ Created topic: " + topicName + " -> " + jndiName)
    
    # Create Dead Letter Queues
    print("\n🔧 Creating Dead Letter Queues...")
    
    dlqQueues = [
        ['DLQ.todo.create.queue', 'jms/DLQ.todo.create.queue', 'Dead letter queue for failed create operations'],
        ['DLQ.todo.update.queue', 'jms/DLQ.todo.update.queue', 'Dead letter queue for failed update operations'],
        ['DLQ.todo.delete.queue', 'jms/DLQ.todo.delete.queue', 'Dead letter queue for failed delete operations'],
        ['DLQ.todo.toggle.queue', 'jms/DLQ.todo.toggle.queue', 'Dead letter queue for failed toggle operations']
    ]
    
    for dlqName, jndiName, description in dlqQueues:
        dlqAttrs = [
            ['name', dlqName],
            ['jndiName', jndiName],
            ['description', description],
            ['type', 'javax.jms.Queue'],
            ['factoryClass', 'org.apache.activemq.command.ActiveMQQueue']
        ]
        
        dlq = AdminConfig.create('GenericJMSDestination', jmsProvider, dlqAttrs)
        
        # Set DLQ properties
        dlqPropertySet = AdminConfig.showAttribute(dlq, 'propertySet')
        if not dlqPropertySet:
            dlqPropertySet = AdminConfig.create('J2EEResourcePropertySet', dlq, [])
        
        # Add physicalName property
        physicalNameProperty = AdminConfig.create('J2EEResourceProperty', dlqPropertySet, [
            ['name', 'physicalName'],
            ['value', dlqName],
            ['type', 'java.lang.String'],
            ['description', 'Physical DLQ name in ActiveMQ']
        ])
        
        print("✅ Created DLQ: " + dlqName + " -> " + jndiName)
    
    # Save the configuration
    print("\n💾 Saving configuration...")
    AdminConfig.save()
    print("✅ Configuration saved successfully")
    
    print("\n=== JMS Configuration Summary ===")
    print("✅ JMS Provider: ActiveMQProvider")
    print("✅ Connection Factory: jms/TodoConnectionFactory")
    print("✅ Queues: 5 operational queues + 4 dead letter queues")
    print("✅ Topics: 2 notification topics")
    print("✅ ActiveMQ Broker: tcp://activemq:61616")
    print("")
    print("🔄 Please restart WebSphere server to apply changes:")
    print("   docker restart appserver")
    print("")
    print("📝 Verify configuration in WebSphere Admin Console:")
    print("   Resources -> JMS -> JMS providers -> ActiveMQProvider")
    
except Exception as e:
    print("❌ Error occurred: " + str(e))
    import traceback
    traceback.print_exc()
    exit(1)

print("=== JMS Configuration Complete ===")
