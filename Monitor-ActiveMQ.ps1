# ActiveMQ Monitoring Script for Todo Application (PowerShell)
# This script provides automated monitoring and alerting for ActiveMQ queues

param(
    [string]$ActiveMQUrl = "http://localhost:8161",
    [string]$Username = "admin",
    [string]$Password = "admin",
    [int]$AlertThreshold = 50
)

# Function to print colored output
function Write-Status {
    param(
        [string]$Status,
        [string]$Message
    )
    
    switch ($Status) {
        "OK" { 
            Write-Host "✅ OK: $Message" -ForegroundColor Green 
        }
        "WARNING" { 
            Write-Host "⚠️ WARNING: $Message" -ForegroundColor Yellow 
        }
        "ERROR" { 
            Write-Host "❌ ERROR: $Message" -ForegroundColor Red 
        }
        "INFO" { 
            Write-Host "ℹ️ INFO: $Message" -ForegroundColor Blue 
        }
    }
}

# Function to make authenticated HTTP requests
function Invoke-ActiveMQRequest {
    param(
        [string]$Url,
        [string]$Username,
        [string]$Password
    )
    
    try {
        $credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${Username}:${Password}"))
        $headers = @{
            "Authorization" = "Basic $credentials"
        }
        
        $response = Invoke-RestMethod -Uri $Url -Headers $headers -TimeoutSec 10
        return $response
    }
    catch {
        return $null
    }
}

# Function to check ActiveMQ connectivity
function Test-ActiveMQConnectivity {
    Write-Host "🔍 Checking ActiveMQ Connectivity..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-ActiveMQRequest -Url "$ActiveMQUrl/admin/" -Username $Username -Password $Password
        if ($response) {
            Write-Status "OK" "ActiveMQ is accessible at $ActiveMQUrl"
            return $true
        }
    }
    catch {
        Write-Status "ERROR" "Cannot connect to ActiveMQ at $ActiveMQUrl"
        return $false
    }
    
    return $false
}

# Function to get queue statistics
function Get-QueueStats {
    param([string]$QueueName)
    
    try {
        $xmlData = Invoke-ActiveMQRequest -Url "$ActiveMQUrl/admin/xml/queues.jsp" -Username $Username -Password $Password
        
        if (-not $xmlData) {
            return @{
                Pending = 0
                Consumers = 0
                Enqueued = 0
                Dequeued = 0
                Error = $true
            }
        }
        
        # Parse XML to find queue statistics
        $xml = [xml]$xmlData
        $queue = $xml.queues.queue | Where-Object { $_.name -eq $QueueName }
        
        if ($queue) {
            return @{
                Pending = [int]$queue.size
                Consumers = [int]$queue.consumerCount
                Enqueued = [int]$queue.enqueueCount
                Dequeued = [int]$queue.dequeueCount
                Error = $false
            }
        }
        else {
            return @{
                Pending = 0
                Consumers = 0
                Enqueued = 0
                Dequeued = 0
                Error = $false
            }
        }
    }
    catch {
        return @{
            Pending = 0
            Consumers = 0
            Enqueued = 0
            Dequeued = 0
            Error = $true
        }
    }
}

# Function to monitor todo queues
function Monitor-TodoQueues {
    Write-Host ""
    Write-Host "📊 Todo Application Queue Status" -ForegroundColor Cyan
    Write-Host "================================="
    
    $queues = @(
        "todo.create.queue",
        "todo.update.queue", 
        "todo.delete.queue",
        "todo.toggle.queue",
        "todo.audit.queue"
    )
    
    $totalPending = 0
    $totalIssues = 0
    
    # Header
    $format = "{0,-20} {1,-8} {2,-9} {3,-9} {4,-9} {5,-6}"
    Write-Host ($format -f "Queue", "Pending", "Consumers", "Enqueued", "Dequeued", "Status")
    Write-Host ($format -f "--------------------", "--------", "---------", "---------", "---------", "------")
    
    foreach ($queue in $queues) {
        $stats = Get-QueueStats -QueueName $queue
        
        if ($stats.Error) {
            Write-Host ($format -f $queue, "ERROR", "ERROR", "ERROR", "ERROR", "ERROR") -ForegroundColor Red
            $totalIssues++
            continue
        }
        
        $totalPending += $stats.Pending
        
        # Determine status
        $status = "OK"
        $statusColor = "Green"
        
        if ($stats.Pending -gt $AlertThreshold) {
            $status = "HIGH"
            $statusColor = "Red"
            $totalIssues++
        }
        elseif ($stats.Pending -gt 10) {
            $status = "WARN"
            $statusColor = "Yellow"
        }
        
        if ($stats.Consumers -eq 0) {
            $status = "NO_CONS"
            $statusColor = "Red"
            $totalIssues++
        }
        
        Write-Host ($format -f $queue, $stats.Pending, $stats.Consumers, $stats.Enqueued, $stats.Dequeued, "") -NoNewline
        Write-Host $status -ForegroundColor $statusColor
    }
    
    Write-Host ""
    Write-Host "📈 Summary: $totalPending total pending messages, $totalIssues issues detected"
}

# Function to monitor dead letter queues
function Monitor-DeadLetterQueues {
    Write-Host ""
    Write-Host "💀 Dead Letter Queue Status" -ForegroundColor Cyan
    Write-Host "============================"
    
    $dlqQueues = @(
        "DLQ.todo.create.queue",
        "DLQ.todo.update.queue",
        "DLQ.todo.delete.queue", 
        "DLQ.todo.toggle.queue"
    )
    
    $totalDLQ = 0
    $dlqIssues = 0
    
    # Header
    $format = "{0,-25} {1,-8} {2,-6}"
    Write-Host ($format -f "DLQ Queue", "Messages", "Status")
    Write-Host ($format -f "-------------------------", "--------", "------")
    
    foreach ($queue in $dlqQueues) {
        $stats = Get-QueueStats -QueueName $queue
        
        if ($stats.Error) {
            Write-Host ($format -f $queue, "ERROR", "") -NoNewline
            Write-Host "ERROR" -ForegroundColor Red
            continue
        }
        
        $totalDLQ += $stats.Pending
        
        $status = "OK"
        $statusColor = "Green"
        
        if ($stats.Pending -gt 0) {
            $status = "FAILED"
            $statusColor = "Red"
            $dlqIssues++
        }
        
        Write-Host ($format -f $queue, $stats.Pending, "") -NoNewline
        Write-Host $status -ForegroundColor $statusColor
    }
    
    Write-Host ""
    if ($totalDLQ -gt 0) {
        Write-Status "WARNING" "$totalDLQ failed messages in dead letter queues - investigate immediately"
    }
    else {
        Write-Status "OK" "No failed messages in dead letter queues"
    }
}

# Function to check system health
function Test-SystemHealth {
    Write-Host ""
    Write-Host "🏥 System Health Check" -ForegroundColor Cyan
    Write-Host "======================"
    
    # Check Docker containers
    try {
        $containers = docker ps --format "table {{.Names}}\t{{.Status}}" | Select-String -Pattern "(activemq|appserver|oracle)"
        
        if ($containers -match "activemq.*Up") {
            Write-Status "OK" "ActiveMQ container is running"
        }
        else {
            Write-Status "WARNING" "ActiveMQ container not running properly"
        }
        
        if ($containers -match "appserver.*Up") {
            Write-Status "OK" "WebSphere application server is running"
        }
        else {
            Write-Status "WARNING" "WebSphere application server not running"
        }
        
        if ($containers -match "oracle.*Up") {
            Write-Status "OK" "Oracle database container is running"
        }
        else {
            Write-Status "WARNING" "Oracle database container not running"
        }
    }
    catch {
        Write-Status "WARNING" "Cannot check Docker container status"
    }
    
    # Check ActiveMQ broker status
    try {
        $brokerInfo = Invoke-ActiveMQRequest -Url "$ActiveMQUrl/api/jolokia/read/org.apache.activemq:type=Broker,brokerName=localhost" -Username $Username -Password $Password
        
        if ($brokerInfo -and $brokerInfo.value.BrokerName -eq "localhost") {
            Write-Status "OK" "ActiveMQ broker is running normally"
        }
        else {
            Write-Status "WARNING" "ActiveMQ broker status unclear"
        }
    }
    catch {
        Write-Status "WARNING" "Cannot access ActiveMQ JMX metrics"
    }
}

# Function to provide recommendations
function Show-Recommendations {
    Write-Host ""
    Write-Host "💡 Recommendations" -ForegroundColor Cyan
    Write-Host "=================="
    
    try {
        # Get total pending messages
        $xmlData = Invoke-ActiveMQRequest -Url "$ActiveMQUrl/admin/xml/queues.jsp" -Username $Username -Password $Password
        
        if ($xmlData) {
            $xml = [xml]$xmlData
            $totalPending = ($xml.queues.queue | Measure-Object -Property size -Sum).Sum
            $dlqCount = ($xml.queues.queue | Where-Object { $_.name -like "DLQ.*" } | Measure-Object -Property size -Sum).Sum
            
            if ($totalPending -gt 50) {
                Write-Host "🚨 High message volume detected ($totalPending pending)" -ForegroundColor Red
                Write-Host "   → Check database connectivity"
                Write-Host "   → Verify circuit breaker status: http://localhost:9080/todo-list-app/resilience/dashboard"
                Write-Host "   → Consider restarting consumers: docker restart appserver"
            }
            
            if ($dlqCount -gt 0) {
                Write-Host "💀 Failed messages detected ($dlqCount in DLQ)" -ForegroundColor Red
                Write-Host "   → Investigate message content: http://localhost:8161/admin/queues.jsp"
                Write-Host "   → Check application logs for errors"
                Write-Host "   → Retry messages after fixing root cause"
            }
        }
    }
    catch {
        Write-Host "⚠️ Cannot analyze queue statistics for recommendations" -ForegroundColor Yellow
    }
    
    # General recommendations
    Write-Host ""
    Write-Host "📋 Daily Actions:"
    Write-Host "   • Monitor queue depths: http://localhost:8161/admin/queues.jsp"
    Write-Host "   • Check resilience dashboard: http://localhost:9080/todo-list-app/resilience/dashboard"
    Write-Host "   • Review application logs for errors"
    Write-Host "   • Verify database connectivity"
}

# Main execution
function Main {
    Write-Host "🚀 ActiveMQ Todo Application Monitor" -ForegroundColor Magenta
    Write-Host "====================================="
    Write-Host "Timestamp: $(Get-Date)"
    Write-Host ""
    
    # Check connectivity first
    if (-not (Test-ActiveMQConnectivity)) {
        Write-Host ""
        Write-Host "❌ Cannot proceed without ActiveMQ connectivity" -ForegroundColor Red
        Write-Host "💡 Try: docker restart activemq"
        return
    }
    
    # Run monitoring checks
    Monitor-TodoQueues
    Monitor-DeadLetterQueues
    Test-SystemHealth
    Show-Recommendations
    
    Write-Host ""
    Write-Host "✅ Monitoring complete - $(Get-Date)" -ForegroundColor Green
    Write-Host "🔄 Run this script regularly or set up as a scheduled task"
}

# Execute main function
Main
