# 🚀 ActiveMQ Operations Guide for Todo Application

## 📋 **Table of Contents**
1. [Access Points & Login](#access-points--login)
2. [Queue Monitoring](#queue-monitoring)
3. [Message Management](#message-management)
4. [Troubleshooting](#troubleshooting)
5. [Performance Monitoring](#performance-monitoring)
6. [Maintenance Operations](#maintenance-operations)

---

## 🔗 **Access Points & Login**

### **ActiveMQ Web Console**
- **URL**: http://localhost:8161
- **Admin Console**: http://localhost:8161/admin/
- **Credentials**: 
  - Username: `admin`
  - Password: `admin`

### **Quick Access Links**
- **Queues**: http://localhost:8161/admin/queues.jsp
- **Topics**: http://localhost:8161/admin/topics.jsp
- **Connections**: http://localhost:8161/admin/connections.jsp
- **Send Message**: http://localhost:8161/admin/send.jsp

---

## 📊 **Queue Monitoring**

### **Todo Application Queues**
Navigate to **Queues** tab to monitor these operational queues:

#### **Primary Queues**
```
📥 todo.create.queue    - New todo creation operations
📝 todo.update.queue    - Todo update operations  
🗑️ todo.delete.queue    - Todo deletion operations
🔄 todo.toggle.queue    - Status toggle operations
📋 todo.audit.queue     - Audit trail messages
```

#### **Dead Letter Queues (DLQ)**
```
💀 DLQ.todo.create.queue - Failed create operations
💀 DLQ.todo.update.queue - Failed update operations
💀 DLQ.todo.delete.queue - Failed delete operations
💀 DLQ.todo.toggle.queue - Failed toggle operations
```

#### **Notification Topics**
```
📢 todo.notifications   - Real-time notifications
📈 todo.statistics      - System metrics
```

### **Key Metrics to Monitor**

#### **Queue Statistics Table**
| Metric | Description | Normal Range | Alert Threshold |
|--------|-------------|--------------|-----------------|
| **Number Of Pending Messages** | Messages waiting to be processed | 0-10 | >50 |
| **Number Of Consumers** | Active message consumers | 1-5 | 0 (no consumers) |
| **Messages Enqueued** | Total messages sent to queue | Increasing | Stagnant |
| **Messages Dequeued** | Total messages processed | Increasing | Not increasing |
| **Queue Size** | Current queue depth | 0-10 | >100 |

#### **How to Check Queue Status**
1. **Navigate to Queues**: http://localhost:8161/admin/queues.jsp
2. **Look for these columns**:
   - `Number Of Pending Messages` - **Most Important**
   - `Number Of Consumers` - Should be > 0
   - `Messages Enqueued` - Total sent
   - `Messages Dequeued` - Total processed

---

## 📨 **Message Management**

### **Viewing Pending Messages**

#### **Step 1: Access Queue Details**
1. Go to **Queues** tab
2. Click on queue name (e.g., `todo.create.queue`)
3. View detailed queue information

#### **Step 2: Browse Messages**
1. Click **"Browse"** link next to queue name
2. See all pending messages with:
   - Message ID
   - Timestamp
   - Message content
   - Properties

#### **Step 3: Message Content Analysis**
```json
// Example message content for todo.create.queue
{
  "operation": "CREATE_TODO",
  "todoId": null,
  "userId": "1",
  "title": "Complete project documentation",
  "description": "Write comprehensive user guide",
  "priority": "HIGH",
  "dueDate": "2025-06-30",
  "timestamp": "2025-06-25T21:45:00Z"
}
```

### **Manual Message Operations**

#### **Send Test Message**
1. **Navigate**: http://localhost:8161/admin/send.jsp
2. **Select Destination**: Choose queue (e.g., `todo.create.queue`)
3. **Message Content**:
```json
{
  "operation": "CREATE_TODO",
  "userId": "1",
  "title": "Test Todo",
  "description": "Testing message queue",
  "priority": "MEDIUM"
}
```
4. **Click "Send"**

#### **Delete Specific Messages**
1. **Browse Queue**: Click queue name → Browse
2. **Select Message**: Check message checkbox
3. **Click "Delete"** to remove message

#### **Purge Entire Queue**
⚠️ **CAUTION**: This deletes ALL messages
1. **Go to Queues** tab
2. **Click "Purge"** next to queue name
3. **Confirm** deletion

---

## 🔄 **Retry Operations**

### **Retry Failed Messages from DLQ**

#### **Method 1: Manual Retry (Individual Messages)**
1. **Access DLQ**: Browse `DLQ.todo.create.queue`
2. **Copy Message Content**: Note the JSON payload
3. **Send to Original Queue**:
   - Go to Send Message page
   - Select original queue (`todo.create.queue`)
   - Paste message content
   - Send message
4. **Delete from DLQ**: Remove original failed message

#### **Method 2: Bulk Retry (All Messages)**
1. **Browse DLQ**: `DLQ.todo.create.queue`
2. **Select All Messages**: Check "Select All"
3. **Copy Messages**: Note all message contents
4. **Send to Original Queue**: Batch send to `todo.create.queue`
5. **Purge DLQ**: Clear dead letter queue

#### **Method 3: Application-Level Retry**
Use the Resilience Dashboard:
1. **Access**: http://localhost:9080/todo-list-app/resilience/dashboard
2. **Reset Circuit Breaker**: If open, reset to allow processing
3. **Monitor Recovery**: Watch queue depths decrease

### **Automated Retry Configuration**
```xml
<!-- In ActiveMQ configuration -->
<redeliveryPolicy>
  <redeliveryPolicy maximumRedeliveries="3" 
                   initialRedeliveryDelay="1000" 
                   redeliveryDelay="5000" 
                   useExponentialBackOff="true" 
                   backOffMultiplier="2.0"/>
</redeliveryPolicy>
```

---

## 🔍 **Troubleshooting Guide**

### **Common Issues & Solutions**

#### **🚨 High Pending Message Count**
**Symptoms**: Queue shows many pending messages
**Causes**:
- Database connection issues
- Circuit breaker open
- Consumer not running
- Processing errors

**Solutions**:
1. **Check Database**: Verify Oracle connection
2. **Check Circuit Breaker**: Reset if open
3. **Restart Consumers**: Restart WebSphere application
4. **Check Logs**: Review application logs

#### **🚨 No Consumers Connected**
**Symptoms**: "Number Of Consumers" shows 0
**Causes**:
- Application not started
- JMS connection factory issues
- Network connectivity problems

**Solutions**:
1. **Restart Application**: Restart todo-list-app
2. **Check JMS Config**: Verify connection factory
3. **Check Network**: Test ActiveMQ connectivity

#### **🚨 Messages Going to DLQ**
**Symptoms**: DLQ queues have messages
**Causes**:
- Database errors
- Invalid message format
- Processing exceptions

**Solutions**:
1. **Check Message Content**: Verify JSON format
2. **Check Database**: Ensure tables exist
3. **Review Logs**: Check application error logs
4. **Retry Messages**: Move from DLQ to original queue

#### **🚨 Memory Issues**
**Symptoms**: ActiveMQ running out of memory
**Solutions**:
1. **Purge Old Messages**: Clean up processed messages
2. **Increase Memory**: Adjust JVM settings
3. **Configure Limits**: Set queue size limits

---

## 📈 **Performance Monitoring**

### **Key Performance Indicators**

#### **Throughput Metrics**
- **Messages/Second**: Enqueue and dequeue rates
- **Processing Time**: Time from enqueue to dequeue
- **Queue Depth**: Average pending messages

#### **System Health Metrics**
- **Memory Usage**: ActiveMQ JVM memory
- **Connection Count**: Active JMS connections
- **Consumer Count**: Active message consumers

### **Monitoring Commands**

#### **REST API Queries**
```bash
# Get broker statistics
curl -u admin:admin "http://localhost:8161/api/jolokia/read/org.apache.activemq:type=Broker,brokerName=localhost"

# Get queue statistics
curl -u admin:admin "http://localhost:8161/api/jolokia/read/org.apache.activemq:type=Broker,brokerName=localhost,destinationType=Queue,destinationName=todo.create.queue"

# Get all queues
curl -u admin:admin "http://localhost:8161/admin/xml/queues.jsp"
```

#### **Health Check Script**
```bash
#!/bin/bash
# Check ActiveMQ health
echo "=== ActiveMQ Health Check ==="

# Check if ActiveMQ is running
if curl -s -u admin:admin http://localhost:8161/admin/ > /dev/null; then
    echo "✅ ActiveMQ is running"
else
    echo "❌ ActiveMQ is not accessible"
    exit 1
fi

# Check queue depths
echo "📊 Queue Status:"
curl -s -u admin:admin "http://localhost:8161/admin/xml/queues.jsp" | grep -E "(todo\.|DLQ\.)"
```

---

## 🛠️ **Maintenance Operations**

### **Daily Maintenance**

#### **Morning Checklist**
- [ ] Check queue depths (should be low)
- [ ] Verify consumer connections
- [ ] Review DLQ for failed messages
- [ ] Check ActiveMQ memory usage

#### **Evening Checklist**
- [ ] Review daily message volumes
- [ ] Clean up processed messages
- [ ] Check for any stuck messages
- [ ] Backup important configurations

### **Weekly Maintenance**

#### **Performance Review**
- [ ] Analyze message processing trends
- [ ] Review error patterns in DLQ
- [ ] Check system resource usage
- [ ] Update monitoring thresholds

#### **Cleanup Operations**
- [ ] Purge old audit messages
- [ ] Clean up DLQ after investigation
- [ ] Archive performance metrics
- [ ] Review and update configurations

### **Emergency Procedures**

#### **ActiveMQ Restart**
```bash
# Restart ActiveMQ container
docker restart activemq

# Wait for startup
sleep 30

# Verify health
curl -u admin:admin http://localhost:8161/admin/
```

#### **Queue Emergency Purge**
⚠️ **Use only in emergencies**
```bash
# Purge all todo queues
curl -u admin:admin -X POST "http://localhost:8161/admin/purgeQueue.action?JMSDestination=todo.create.queue"
curl -u admin:admin -X POST "http://localhost:8161/admin/purgeQueue.action?JMSDestination=todo.update.queue"
curl -u admin:admin -X POST "http://localhost:8161/admin/purgeQueue.action?JMSDestination=todo.delete.queue"
curl -u admin:admin -X POST "http://localhost:8161/admin/purgeQueue.action?JMSDestination=todo.toggle.queue"
```

---

## 📞 **Quick Reference**

### **Emergency Contacts**
- **ActiveMQ Console**: http://localhost:8161/admin/
- **Resilience Dashboard**: http://localhost:9080/todo-list-app/resilience/dashboard
- **Application Logs**: Check WebSphere logs

### **Common Commands**
```bash
# Check ActiveMQ status
docker ps | grep activemq

# View ActiveMQ logs
docker logs activemq --tail 50

# Restart ActiveMQ
docker restart activemq

# Access ActiveMQ shell
docker exec -it activemq bash
```

### **Alert Thresholds**
- **Queue Depth**: >50 messages
- **DLQ Messages**: >0 messages
- **No Consumers**: 0 active consumers
- **Memory Usage**: >80% JVM memory

---

**🎯 Remember: Regular monitoring prevents issues. Check queues daily and investigate any anomalies immediately!**
