package com.todoapp.controller;

import com.todoapp.entity.Todo;
import com.todoapp.service.ResilientTodoService;
import com.todoapp.service.TodoService.TodoStats;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * TodoController - Servlet controller for handling todo operations
 * Handles all CRUD operations and page routing for the todo application
 */
public class TodoController extends HttpServlet {
    private static final Logger logger = Logger.getLogger(TodoController.class.getName());
    private static final long serialVersionUID = 1L;
    private static final Long DEFAULT_USER_ID = 1L; // Default user for demo
    
    private ResilientTodoService todoService;

    @Override
    public void init() throws ServletException {
        super.init();
        try {
            todoService = new ResilientTodoService();
            logger.info("TodoController initialized successfully");
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to initialize TodoController", e);
            throw new ServletException("Failed to initialize TodoController", e);
        }
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        String action = request.getParameter("action");
        
        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                // Main todos page
                handleTodosPage(request, response);
            } else if (pathInfo.equals("/api")) {
                // API endpoint for AJAX requests
                handleApiRequest(request, response);
            } else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error in doGet", e);
            request.setAttribute("error", "An error occurred: " + e.getMessage());
            request.getRequestDispatcher("/todos.jsp").forward(request, response);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Set response content type to JSON
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        String action = request.getParameter("action");
        logger.info("Received POST request with action: " + action);

        // Debug: Log all parameters
        logger.info("All request parameters:");
        java.util.Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String paramValue = request.getParameter(paramName);
            logger.info("  " + paramName + " = " + paramValue);
        }

        try {
            if ("create".equals(action)) {
                handleCreateTodo(request, response);
            } else if ("update".equals(action)) {
                handleUpdateTodo(request, response);
            } else if ("toggle".equals(action)) {
                handleToggleTodo(request, response);
            } else if ("delete".equals(action)) {
                handleDeleteTodo(request, response);
            } else {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"success\": false, \"message\": \"Invalid action: " + action + "\"}");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error in doPost", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"success\": false, \"message\": \"" + e.getMessage() + "\"}");
        }
    }

    /**
     * Handle main todos page
     */
    private void handleTodosPage(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String filter = request.getParameter("filter");
        List<Todo> todos;
        
        if ("completed".equals(filter)) {
            todos = todoService.getTodosByStatus(DEFAULT_USER_ID, true);
        } else if ("pending".equals(filter)) {
            todos = todoService.getTodosByStatus(DEFAULT_USER_ID, false);
        } else {
            todos = todoService.getAllTodos(DEFAULT_USER_ID);
        }
        
        TodoStats stats = todoService.getTodoStats(DEFAULT_USER_ID);
        
        request.setAttribute("todos", todos);
        request.setAttribute("stats", stats);
        request.setAttribute("currentFilter", filter != null ? filter : "all");
        
        request.getRequestDispatcher("/todos.jsp").forward(request, response);
    }

    /**
     * Handle API requests for AJAX
     */
    private void handleApiRequest(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String action = request.getParameter("action");
        
        if ("stats".equals(action)) {
            TodoStats stats = todoService.getTodoStats(DEFAULT_USER_ID);
            response.getWriter().write(String.format(
                "{\"total\": %d, \"completed\": %d, \"pending\": %d, \"overdue\": %d, \"completionPercentage\": %.1f}",
                stats.getTotal(), stats.getCompleted(), stats.getPending(), 
                stats.getOverdue(), stats.getCompletionPercentage()
            ));
        } else {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"success\": false, \"message\": \"Invalid API action\"}");
        }
    }

    /**
     * Handle creating a new todo
     */
    private void handleCreateTodo(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String title = request.getParameter("title");
        String description = request.getParameter("description");
        String priority = request.getParameter("priority");
        String dueDateStr = request.getParameter("dueDate");
        
        if (title == null || title.trim().isEmpty()) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"success\": false, \"message\": \"Title is required\"}");
            return;
        }
        
        Todo todo = new Todo();
        todo.setTitle(title.trim());
        todo.setDescription(description != null ? description.trim() : "");
        todo.setPriority(priority != null ? priority : "MEDIUM");
        todo.setUserId(DEFAULT_USER_ID);
        
        // Parse due date if provided
        if (dueDateStr != null && !dueDateStr.trim().isEmpty()) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                java.util.Date parsedDate = sdf.parse(dueDateStr);
                todo.setDueDate(new Date(parsedDate.getTime()));
            } catch (ParseException e) {
                logger.log(Level.WARNING, "Invalid date format: " + dueDateStr, e);
            }
        }
        
        Todo createdTodo = todoService.createTodo(todo);
        
        if (createdTodo != null) {
            response.setContentType("application/json");
            response.getWriter().write("{\"success\": true, \"message\": \"Todo created successfully\"}");
        } else {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"success\": false, \"message\": \"Failed to create todo\"}");
        }
    }

    /**
     * Handle updating an existing todo
     */
    private void handleUpdateTodo(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String todoIdStr = request.getParameter("todoId");
        String title = request.getParameter("title");
        String description = request.getParameter("description");
        String priority = request.getParameter("priority");
        String dueDateStr = request.getParameter("dueDate");
        String completedStr = request.getParameter("completed");
        
        if (todoIdStr == null || title == null || title.trim().isEmpty()) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"success\": false, \"message\": \"Todo ID and title are required\"}");
            return;
        }
        
        try {
            Long todoId = Long.parseLong(todoIdStr);
            Todo todo = todoService.getTodoById(todoId);
            
            if (todo == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("{\"success\": false, \"message\": \"Todo not found\"}");
                return;
            }
            
            todo.setTitle(title.trim());
            todo.setDescription(description != null ? description.trim() : "");
            todo.setPriority(priority != null ? priority : "MEDIUM");
            todo.setCompleted("true".equals(completedStr) || "1".equals(completedStr));
            
            // Parse due date if provided
            if (dueDateStr != null && !dueDateStr.trim().isEmpty()) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    java.util.Date parsedDate = sdf.parse(dueDateStr);
                    todo.setDueDate(new Date(parsedDate.getTime()));
                } catch (ParseException e) {
                    logger.log(Level.WARNING, "Invalid date format: " + dueDateStr, e);
                }
            } else {
                todo.setDueDate(null);
            }
            
            Todo updatedTodo = todoService.updateTodo(todo);
            
            if (updatedTodo != null) {
                response.setContentType("application/json");
                response.getWriter().write("{\"success\": true, \"message\": \"Todo updated successfully\"}");
            } else {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"success\": false, \"message\": \"Failed to update todo\"}");
            }
            
        } catch (NumberFormatException e) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"success\": false, \"message\": \"Invalid todo ID\"}");
        }
    }

    /**
     * Handle toggling todo completion status
     */
    private void handleToggleTodo(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String todoIdStr = request.getParameter("todoId");
        
        if (todoIdStr == null) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"success\": false, \"message\": \"Todo ID is required\"}");
            return;
        }
        
        try {
            Long todoId = Long.parseLong(todoIdStr);
            boolean success = todoService.toggleTodoStatus(todoId);
            
            response.setContentType("application/json");
            if (success) {
                response.getWriter().write("{\"success\": true, \"message\": \"Todo status updated\"}");
            } else {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"success\": false, \"message\": \"Failed to update todo status\"}");
            }
            
        } catch (NumberFormatException e) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"success\": false, \"message\": \"Invalid todo ID\"}");
        }
    }

    /**
     * Handle deleting a todo
     */
    private void handleDeleteTodo(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String todoIdStr = request.getParameter("todoId");
        
        if (todoIdStr == null) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"success\": false, \"message\": \"Todo ID is required\"}");
            return;
        }
        
        try {
            Long todoId = Long.parseLong(todoIdStr);
            boolean success = todoService.deleteTodo(todoId);
            
            response.setContentType("application/json");
            if (success) {
                response.getWriter().write("{\"success\": true, \"message\": \"Todo deleted successfully\"}");
            } else {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"success\": false, \"message\": \"Failed to delete todo\"}");
            }
            
        } catch (NumberFormatException e) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"success\": false, \"message\": \"Invalid todo ID\"}");
        }
    }
}
