# Simple ActiveMQ Test Script for Todo Application

$ActiveMQUrl = "http://localhost:8161"
$Username = "admin"
$Password = "admin"

Write-Host "🚀 ActiveMQ Quick Test" -ForegroundColor Magenta
Write-Host "======================"
Write-Host "Timestamp: $(Get-Date)"
Write-Host ""

# Test ActiveMQ connectivity
Write-Host "🔍 Testing ActiveMQ Connectivity..." -ForegroundColor Cyan

try {
    $credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${Username}:${Password}"))
    $headers = @{ "Authorization" = "Basic $credentials" }
    
    $response = Invoke-WebRequest -Uri "$ActiveMQUrl/admin/" -Headers $headers -TimeoutSec 10 -UseBasicParsing
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ ActiveMQ is accessible at $ActiveMQUrl" -ForegroundColor Green
    }
    else {
        Write-Host "❌ ActiveMQ returned status code: $($response.StatusCode)" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Cannot connect to ActiveMQ: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Try: docker restart activemq" -ForegroundColor Yellow
    exit 1
}

# Test queue XML endpoint
Write-Host ""
Write-Host "📊 Testing Queue Information..." -ForegroundColor Cyan

try {
    $queueResponse = Invoke-WebRequest -Uri "$ActiveMQUrl/admin/xml/queues.jsp" -Headers $headers -TimeoutSec 10 -UseBasicParsing
    
    if ($queueResponse.StatusCode -eq 200) {
        Write-Host "✅ Queue information accessible" -ForegroundColor Green
        
        # Simple queue analysis
        $content = $queueResponse.Content
        
        if ($content -match '<queues>') {
            Write-Host "✅ Queue XML format is valid" -ForegroundColor Green
            
            # Count todo queues
            $todoQueues = ([regex]::Matches($content, 'name="todo\.')).Count
            $dlqQueues = ([regex]::Matches($content, 'name="DLQ\.')).Count
            
            Write-Host "📋 Found $todoQueues todo queues and $dlqQueues DLQ queues" -ForegroundColor Blue
        }
        else {
            Write-Host "⚠️ Unexpected queue XML format" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "❌ Queue endpoint returned status code: $($queueResponse.StatusCode)" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Cannot access queue information: $($_.Exception.Message)" -ForegroundColor Red
}

# Test broker status
Write-Host ""
Write-Host "🏥 Testing Broker Status..." -ForegroundColor Cyan

try {
    $brokerResponse = Invoke-WebRequest -Uri "$ActiveMQUrl/api/jolokia/read/org.apache.activemq:type=Broker,brokerName=localhost" -Headers $headers -TimeoutSec 10 -UseBasicParsing
    
    if ($brokerResponse.StatusCode -eq 200) {
        Write-Host "✅ Broker JMX endpoint accessible" -ForegroundColor Green
        
        $brokerData = $brokerResponse.Content | ConvertFrom-Json
        if ($brokerData.value.BrokerName -eq "localhost") {
            Write-Host "✅ Broker 'localhost' is running normally" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️ Unexpected broker name: $($brokerData.value.BrokerName)" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "❌ Broker endpoint returned status code: $($brokerResponse.StatusCode)" -ForegroundColor Red
    }
}
catch {
    Write-Host "⚠️ Cannot access broker JMX endpoint: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "ℹ️ This is normal if JMX is not fully configured" -ForegroundColor Blue
}

# Check Docker containers
Write-Host ""
Write-Host "🐳 Checking Docker Containers..." -ForegroundColor Cyan

try {
    $dockerOutput = docker ps --format "table {{.Names}}\t{{.Status}}" 2>$null
    
    if ($dockerOutput) {
        if ($dockerOutput -match "activemq.*Up") {
            Write-Host "✅ ActiveMQ container is running" -ForegroundColor Green
        }
        else {
            Write-Host "❌ ActiveMQ container not found or not running" -ForegroundColor Red
        }
        
        if ($dockerOutput -match "appserver.*Up") {
            Write-Host "✅ WebSphere container is running" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️ WebSphere container not found or not running" -ForegroundColor Yellow
        }
        
        if ($dockerOutput -match "oracle.*Up") {
            Write-Host "✅ Oracle container is running" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️ Oracle container not found or not running" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "❌ Cannot execute docker commands" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Error checking Docker containers: $($_.Exception.Message)" -ForegroundColor Red
}

# Summary and recommendations
Write-Host ""
Write-Host "📋 Quick Access Links:" -ForegroundColor Cyan
Write-Host "• ActiveMQ Console: http://localhost:8161/admin/"
Write-Host "• Queue Monitor: http://localhost:8161/admin/queues.jsp"
Write-Host "• Send Message: http://localhost:8161/admin/send.jsp"
Write-Host "• Todo App: http://localhost:9080/todo-list-app/todos"
Write-Host "• Resilience Dashboard: http://localhost:9080/todo-list-app/resilience/dashboard"

Write-Host ""
Write-Host "💡 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Open ActiveMQ console and check queue depths"
Write-Host "2. Verify todo application is deployed and working"
Write-Host "3. Test message flow by creating/updating todos"
Write-Host "4. Monitor resilience dashboard for circuit breaker status"

Write-Host ""
Write-Host "✅ ActiveMQ test complete - $(Get-Date)" -ForegroundColor Green
