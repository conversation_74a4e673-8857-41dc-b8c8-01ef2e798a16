# Simple ActiveMQ Health Check Script

$ActiveMQUrl = "http://localhost:8161"
$Username = "admin"
$Password = "admin"

Write-Host "ActiveMQ Health Check" -ForegroundColor Magenta
Write-Host "====================="
Write-Host "Timestamp: $(Get-Date)"
Write-Host ""

# Test ActiveMQ connectivity
Write-Host "Testing ActiveMQ Connectivity..." -ForegroundColor Cyan

try {
    $credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${Username}:${Password}"))
    $headers = @{ "Authorization" = "Basic $credentials" }
    
    $response = Invoke-WebRequest -Uri "$ActiveMQUrl/admin/" -Headers $headers -TimeoutSec 10 -UseBasicParsing
    
    if ($response.StatusCode -eq 200) {
        Write-Host "SUCCESS: ActiveMQ is accessible" -ForegroundColor Green
    }
    else {
        Write-Host "ERROR: ActiveMQ returned status code: $($response.StatusCode)" -ForegroundColor Red
    }
}
catch {
    Write-Host "ERROR: Cannot connect to ActiveMQ: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "TRY: docker restart activemq" -ForegroundColor Yellow
    exit 1
}

# Test queue information
Write-Host ""
Write-Host "Testing Queue Access..." -ForegroundColor Cyan

try {
    $queueResponse = Invoke-WebRequest -Uri "$ActiveMQUrl/admin/xml/queues.jsp" -Headers $headers -TimeoutSec 10 -UseBasicParsing
    
    if ($queueResponse.StatusCode -eq 200) {
        Write-Host "SUCCESS: Queue information accessible" -ForegroundColor Green
        
        $content = $queueResponse.Content
        
        # Count queues
        $todoQueues = ([regex]::Matches($content, 'name="todo\.')).Count
        $dlqQueues = ([regex]::Matches($content, 'name="DLQ\.')).Count
        
        Write-Host "INFO: Found $todoQueues todo queues and $dlqQueues DLQ queues" -ForegroundColor Blue
        
        # Check for pending messages
        $pendingMatches = [regex]::Matches($content, 'size="(\d+)"')
        $totalPending = 0
        foreach ($match in $pendingMatches) {
            $totalPending += [int]$match.Groups[1].Value
        }
        
        if ($totalPending -gt 0) {
            Write-Host "WARNING: $totalPending total pending messages found" -ForegroundColor Yellow
        }
        else {
            Write-Host "SUCCESS: No pending messages" -ForegroundColor Green
        }
    }
}
catch {
    Write-Host "ERROR: Cannot access queue information: $($_.Exception.Message)" -ForegroundColor Red
}

# Check Docker containers
Write-Host ""
Write-Host "Checking Docker Containers..." -ForegroundColor Cyan

try {
    $dockerOutput = docker ps --format "table {{.Names}}\t{{.Status}}" 2>$null
    
    if ($dockerOutput) {
        if ($dockerOutput -match "activemq.*Up") {
            Write-Host "SUCCESS: ActiveMQ container is running" -ForegroundColor Green
        }
        else {
            Write-Host "ERROR: ActiveMQ container not running" -ForegroundColor Red
        }
        
        if ($dockerOutput -match "appserver.*Up") {
            Write-Host "SUCCESS: WebSphere container is running" -ForegroundColor Green
        }
        else {
            Write-Host "WARNING: WebSphere container not running" -ForegroundColor Yellow
        }
        
        if ($dockerOutput -match "oracle.*Up") {
            Write-Host "SUCCESS: Oracle container is running" -ForegroundColor Green
        }
        else {
            Write-Host "WARNING: Oracle container not running" -ForegroundColor Yellow
        }
    }
}
catch {
    Write-Host "ERROR: Cannot check Docker containers" -ForegroundColor Red
}

# Summary
Write-Host ""
Write-Host "Quick Access URLs:" -ForegroundColor Cyan
Write-Host "- ActiveMQ Console: http://localhost:8161/admin/"
Write-Host "- Queue Monitor: http://localhost:8161/admin/queues.jsp"
Write-Host "- Todo App: http://localhost:9080/todo-list-app/todos"

Write-Host ""
Write-Host "Health check complete - $(Get-Date)" -ForegroundColor Green
